/*
  Warnings:

  - A unique constraint covering the columns `[zaloUserId]` on the table `User` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `imei` to the `User` table without a default value. This is not possible if the table is not empty.
  - Added the required column `zaloUserId` to the `User` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "MessageType" AS ENUM ('TEXT', 'MEDIA', 'STICKER');

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "imei" TEXT NOT NULL,
ADD COLUMN     "zaloUserId" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "Conversation" (
    "id" SERIAL NOT NULL,
    "uidFrom" TEXT NOT NULL,
    "uidTo" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Conversation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Message" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "conversationId" INTEGER NOT NULL,
    "content" JSONB NOT NULL,
    "type" "MessageType" NOT NULL,
    "mediaUrl" TEXT,
    "stickerId" TEXT,
    "uidFrom" TEXT NOT NULL,
    "uidTo" TEXT NOT NULL,
    "msgId" TEXT,
    "msgType" TEXT,
    "isFromUser" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Message_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Conversation_uidFrom_idx" ON "Conversation"("uidFrom");

-- CreateIndex
CREATE INDEX "Conversation_uidTo_idx" ON "Conversation"("uidTo");

-- CreateIndex
CREATE UNIQUE INDEX "Conversation_uidFrom_uidTo_key" ON "Conversation"("uidFrom", "uidTo");

-- CreateIndex
CREATE UNIQUE INDEX "User_zaloUserId_key" ON "User"("zaloUserId");

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
