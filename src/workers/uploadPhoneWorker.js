const { Worker } = require("bullmq");
const { redis } = require("../shared/instances");
const { gemini_ocr } = require("../services/ocr");
const { assign } = require("../services/assign.service");

const createUploadPhoneWorker = () => {
  const worker = new Worker(
    "upload_phone",
    async (job) => {
      const { files, storage_code, storage, user } = job.data;
      const uid = user.uid;
      console.log(`Processing upload job ${job.id} for task ${taskId}`);
      console.log(
        `Files count: ${
          files?.length || 0
        }, storage: ${storage_code}, uid: ${uid}`
      );

      try {
        const ocrResults = await gemini_ocr(files.map((f) => f.buffer));
        const phone_numbers = ocrResults
          .map((r) => r.phone_number)
          .filter(Boolean)
          .map((phoneNumber) => phoneNumber.replace(/^0+/, "")) // Remove leading zeros
          .filter((phoneNumber) => phoneNumber.length > 0) // Remove empty strings
          .filter(
            (phoneNumber, index, array) => array.indexOf(phoneNumber) === index
          );

        const result = await assign(
          { phone_numbers, storage_code },
          user,
          storage
        );
        return result;
      } catch (error) {
        console.error(`Upload job ${job.id} failed:`, error.message);
        throw error;
      }
    },
    { connection: redis }
  );

  // Handle worker events
  worker.on("completed", (job) => {
    console.log(`Upload job ${job.id} completed successfully`);
  });

  worker.on("failed", (job, err) => {
    console.error(`Upload job ${job.id} failed:`, err.message);
  });

  worker.on("error", (err) => {
    console.error("Upload Worker error:", err);
  });

  console.log("Upload Phone Worker started and listening for jobs...");
  return worker;
};

// Export the function for use in other modules
module.exports = createUploadPhoneWorker;

// If this file is run directly, start the worker
if (require.main === module) {
  createUploadPhoneWorker();
}
