const { authenticate<PERSON>ith<PERSON>D } = require("../middleware/auth");
const { assign, check_assign_status } = require("../services/assign.service");

module.exports = async (fastify) => {
  fastify.post(
    "/assign_phone",
    {
      preHandler: authenticateWithUID,
      schema: {
        body: {
          type: "object",
          required: ["phone_numbers", "storage_code"],
          properties: {
            phone_numbers: { type: "array", items: { type: "string" } },
            storage_code: { type: "string" },
          },
        },
      },
    },
    async (request, reply) => {
      const result = await assign(request.body, request.user, request.storage);
      if (result.code !== 200) {
        return reply.code(result.code).send(result);
      }
      return result;
    }
  );

  fastify.get(
    "/check_assign_status",
    {
      schema: {
        querystring: {
          type: "object",
          required: ["task_id"],
          properties: {
            task_id: { type: "string" },
          },
        },
      },
    },
    async (request, reply) => {
      const { task_id } = request.query;
      const result = await check_assign_status(task_id);
      if (result.code !== 200) {
        return reply.code(result.code).send(result);
      }
      return result;
    }
  );
};
